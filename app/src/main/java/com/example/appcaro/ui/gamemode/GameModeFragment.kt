package com.example.appcaro.ui.gamemode

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController

@Composable
fun GameModeFragment(navController: NavHostController, playerMode: String?) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Top
    ) {
        Text(
            text = "Chọn chế độ game",
            style = MaterialTheme.typography.headlineLarge.copy(color = Color.White, fontWeight = FontWeight.Bold)
        )

        Spacer(modifier = Modifier.height(32.dp))

        GameModeCard(
            title = "Caro 3x3",
            description = "Tr<PERSON> chơi cổ điển - Cần 3 quân liên tiếp để thắng",
            onClick = { navController.navigate("game/$playerMode/3x3") }
        )

        Spacer(modifier = Modifier.height(16.dp))

        GameModeCard(
            title = "Caro 10x10",
            description = "Bàn cờ mở rộng - Cần 5 quân liên tiếp để thắng",
            onClick = { navController.navigate("game/$playerMode/10x10") }
        )

        Spacer(modifier = Modifier.height(16.dp))

        GameModeCard(
            title = "Caro Vô Hạn",
            description = "Bàn cờ không giới hạn - Cần 5 quân liên tiếp để thắng",
            onClick = { navController.navigate("game/$playerMode/infinite") }
        )
    }
}

@Composable
fun GameModeCard(title: String, description: String, onClick: () -> Unit) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.Start
        ) {
            Text(text = title, fontWeight = FontWeight.Bold)
            Spacer(modifier = Modifier.height(8.dp))
            Text(text = description)
        }
    }
}
