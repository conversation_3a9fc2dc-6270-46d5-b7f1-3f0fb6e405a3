package com.example.appcaro.ui.game

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.fragment.app.Fragment
import androidx.navigation.NavHostController


class GameFragment(
    private val navController: NavHostController,
    private val playerMode: String?,
    private val gameMode: String?
) : Fragment() {

    private var _binding: FragmentGameBinding? = null
    private val binding get() = _binding!!
    private val gameViewModel: GameViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentGameBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val boardSize = BoardSize.valueOf(gameMode ?: "SIZE_3X3")
        gameViewModel.initGame(boardSize)

        // Setup UI and listeners
        setupUI()
        observeGameState()
    }

    private fun setupUI() {
        // Setup UI elements based on the game mode
    }

    private fun observeGameState() {
        gameViewModel.gameState.observe(viewLifecycleOwner) { gameState ->
            // Update UI based on game state
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
