package com.example.appcaro.ui.playermode

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController

@Composable
fun PlayerModeFragment(navController: NavHostController) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Top
    ) {
        Text(
            text = "Chọn đối thủ",
            style = MaterialTheme.typography.headlineLarge.copy(color = Color.White, fontWeight = FontWeight.Bold)
        )

        Spacer(modifier = Modifier.height(32.dp))

        PlayerModeCard(
            title = "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON>",
            description = "Chơi với bạn bè hoặc gia đình",
            onClick = { navController.navigate("game_mode/human-vs-human") }
        )

        Spacer(modifier = Modifier.height(16.dp))

        PlayerModeCard(
            title = "Người vs Máy",
            description = "Thách thức với AI thông minh",
            onClick = { /* Handle AI Mode */ },
            enabled = false // Disable for now
        )
    }
}

@Composable
fun PlayerModeCard(title: String, description: String, onClick: () -> Unit, enabled: Boolean = true) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(enabled = enabled) { onClick() }
            .padding(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.Start
        ) {
            Text(text = title, fontWeight = FontWeight.Bold)
            Spacer(modifier = Modifier.height(8.dp))
            Text(text = description)
        }
    }
}
