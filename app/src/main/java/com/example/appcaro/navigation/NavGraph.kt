package com.example.appcaro.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.example.appcaro.ui.game.GameFragment
import com.example.appcaro.ui.gamemode.GameModeFragment
import com.example.appcaro.ui.home.HomeFragment
import com.example.appcaro.ui.playermode.PlayerModeFragment


@Composable
fun SetupNavGraph(navController: NavHostController) {
    NavHost(navController = navController, startDestination = "home") {
        composable("home") { HomeFragment(navController) }
        composable("player_mode") { PlayerModeFragment(navController) }
        composable("game_mode/{playerMode}") { backStackEntry ->
            val playerMode = backStackEntry.arguments?.getString("playerMode")
            GameModeFragment(navController, playerMode)
        }
        composable("game/{playerMode}/{gameMode}") { backStackEntry ->
            val playerMode = backStackEntry.arguments?.getString("playerMode")
            val gameMode = backStackEntry.arguments?.getString("gameMode")
            GameFragment(navController, playerMode, gameMode)
        }
    }
}
