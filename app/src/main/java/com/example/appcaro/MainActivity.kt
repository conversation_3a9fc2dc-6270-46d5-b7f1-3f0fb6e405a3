package com.example.appcaro

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.navigation.compose.rememberNavController
import com.example.appcaro.navigation.SetupNavGraph
import com.example.appcaro.theme.AppCaroTheme


class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            AppCaroTheme {
                val navController = rememberNavController()
                SetupNavGraph(navController)
            }
        }
    }
}